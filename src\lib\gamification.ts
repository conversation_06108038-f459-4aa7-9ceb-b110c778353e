import { createClient } from '@/lib/supabase/server'
import { createClient as createAdminClient } from '@supabase/supabase-js'

// Create admin client for bypassing RLS when needed
const supabaseAdmin = createAdminClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export interface UserLevel {
  id: string
  level: number
  name: string
  minimum_points: number
  discount_percentage: number
  points_multiplier: number
}

export interface GiftThreshold {
  id: string
  threshold_points: number
  gift_product_ids: string[]
  is_active: boolean
}

export interface LeagueConfig {
  id: string
  league_1_discount: number
  league_2_discount: number
  league_3_discount: number
  league_4_discount: number
  league_5_discount: number
  global_multiplier: number
  multiplier_description: string
  is_active: boolean
}

// Default gamification settings
const DEFAULT_POINTS_PER_CHF = parseFloat(process.env.POINTS_PER_CHF || '1')

/**
 * Get points per CHF from admin settings
 */
export async function getPointsPerCHF(): Promise<number> {
  try {
    const { data: settings } = await supabaseAdmin
      .from('site_settings')
      .select('points_per_chf')
      .maybeSingle()

    const pointsPerCHF = settings?.points_per_chf || DEFAULT_POINTS_PER_CHF
    console.log(`🎮 Gamification: Points per CHF: ${pointsPerCHF}`)
    return pointsPerCHF
  } catch (error) {
    console.error('Error fetching points per CHF setting:', error)
    return DEFAULT_POINTS_PER_CHF
  }
}

/**
 * Get league configuration
 */
export async function getLeagueConfig(): Promise<LeagueConfig | null> {
  const { data: config } = await supabaseAdmin
    .from('league_config')
    .select('*')
    .eq('is_active', true)
    .single()

  return config || null
}

/**
 * Calculate league number from level (every 10 levels = 1 league)
 */
export function calculateLeague(level: number): number {
  return Math.ceil(level / 10)
}

/**
 * Get league discount percentage based on league number
 */
export async function getLeagueDiscount(league: number): Promise<number> {
  const config = await getLeagueConfig()
  if (!config) return 0

  switch (league) {
    case 1: return config.league_1_discount
    case 2: return config.league_2_discount
    case 3: return config.league_3_discount
    case 4: return config.league_4_discount
    case 5: return config.league_5_discount
    default: return config.league_5_discount // Max discount for higher leagues
  }
}

/**
 * Calculate points earned for a purchase amount (simplified - always uses global multiplier)
 */
export async function calculatePointsEarned(amount: number): Promise<number> {
  const pointsPerCHF = await getPointsPerCHF()
  const config = await getLeagueConfig()
  const globalMultiplier = config?.global_multiplier || 1.0

  const basePoints = amount * pointsPerCHF
  return Math.floor(basePoints * globalMultiplier)
}

/**
 * Get user's current level based on total points
 */
export async function getUserLevel(totalPoints: number): Promise<UserLevel | null> {
  // Use admin client to ensure we can read user levels
  const { data: levels } = await supabaseAdmin
    .from('user_levels')
    .select('*')
    .lte('minimum_points', totalPoints)
    .order('level', { ascending: false })
    .limit(1)

  return levels?.[0] || null
}

/**
 * Get all user levels
 */
export async function getAllUserLevels(): Promise<UserLevel[]> {
  const supabase = await createClient()
  
  const { data: levels } = await supabase
    .from('user_levels')
    .select('*')
    .order('level')

  return levels || []
}

/**
 * Update user's level and points after a purchase
 */
export async function updateUserGamification(
  userId: string,
  purchaseAmount: number,
  orderId?: string
): Promise<{ newLevel: UserLevel | null; pointsEarned: number }> {
  console.log(`🎮 Gamification: Starting update for user ${userId}, purchase amount: ${purchaseAmount}`)

  // Use admin client to ensure we can read and update user data
  const { data: user, error: userError } = await supabaseAdmin
    .from('users')
    .select('lifetime_spend, current_level, total_points')
    .eq('id', userId)
    .single()

  if (userError || !user) {
    console.error('🎮 Gamification: Error fetching user data:', userError)
    return { newLevel: null, pointsEarned: 0 }
  }

  console.log(`🎮 Gamification: Current user data:`, {
    lifetime_spend: user.lifetime_spend,
    current_level: user.current_level,
    total_points: user.total_points
  })

  const newLifetimeSpend = (user.lifetime_spend || 0) + purchaseAmount

  // Get current level info based on current points
  const currentLevel = await getUserLevel(user.total_points || 0)
  console.log(`🎮 Gamification: Current level:`, currentLevel)

  // Calculate points earned for this purchase (simplified - no level multiplier)
  const pointsEarned = await calculatePointsEarned(purchaseAmount)
  const newTotalPoints = (user.total_points || 0) + pointsEarned

  console.log(`🎮 Gamification: Points calculation:`, {
    purchaseAmount,
    globalMultiplier: (await getLeagueConfig())?.global_multiplier || 1.0,
    pointsEarned,
    newTotalPoints
  })

  // Determine new level based on new total points
  const newLevel = await getUserLevel(newTotalPoints)
  console.log(`🎮 Gamification: New level:`, newLevel)

  // Update user using admin client
  const { error: updateError } = await supabaseAdmin
    .from('users')
    .update({
      lifetime_spend: newLifetimeSpend,
      current_level: newLevel?.level || 1,
      total_points: newTotalPoints
    })
    .eq('id', userId)

  if (updateError) {
    console.error('🎮 Gamification: Error updating user:', updateError)
    return { newLevel: null, pointsEarned: 0 }
  }

  // Check and assign gifts based on new points total
  try {
    await checkAndAssignGifts(userId, newTotalPoints, user.total_points || 0, orderId)
  } catch (giftError) {
    console.error('🎁 Gift System: Error checking/assigning gifts:', giftError)
    // Don't fail the gamification update for gift errors
  }

  console.log(`🎮 Gamification: Successfully updated user ${userId} with ${pointsEarned} points`)
  return { newLevel, pointsEarned }
}

/**
 * Get applicable gift thresholds for user points
 */
export async function getApplicableGifts(userPoints: number): Promise<GiftThreshold[]> {
  const supabase = await createClient()

  const { data: thresholds } = await supabase
    .from('gift_thresholds')
    .select('*')
    .eq('is_active', true)
    .lte('threshold_points', userPoints)
    .order('threshold_points', { ascending: false })

  return thresholds || []
}

/**
 * Get next gift threshold for progress display
 */
export async function getNextGiftThreshold(userPoints: number): Promise<GiftThreshold | null> {
  const supabase = await createClient()

  const { data: thresholds } = await supabase
    .from('gift_thresholds')
    .select('*')
    .eq('is_active', true)
    .gt('threshold_points', userPoints)
    .order('threshold_points', { ascending: true })
    .limit(1)

  return thresholds?.[0] || null
}

/**
 * Check and assign gifts based on user points
 */
export async function checkAndAssignGifts(
  userId: string,
  newTotalPoints: number,
  oldTotalPoints: number,
  orderId?: string
): Promise<void> {
  console.log(`🎁 Gift System: Checking gifts for user ${userId}, old points: ${oldTotalPoints}, new points: ${newTotalPoints}`)

  // Get all active gift thresholds
  const { data: thresholds } = await supabaseAdmin
    .from('gift_thresholds')
    .select('*')
    .eq('is_active', true)
    .order('threshold_points', { ascending: true })

  if (!thresholds || thresholds.length === 0) {
    console.log('🎁 Gift System: No active gift thresholds found')
    return
  }

  // Find thresholds that were just reached (old points < threshold <= new points)
  const newlyReachedThresholds = thresholds.filter(threshold =>
    oldTotalPoints < threshold.threshold_points && newTotalPoints >= threshold.threshold_points
  )

  if (newlyReachedThresholds.length === 0) {
    console.log('🎁 Gift System: No new gift thresholds reached')
    return
  }

  console.log(`🎁 Gift System: ${newlyReachedThresholds.length} new gift thresholds reached`)

  // Check which gifts haven't been assigned yet
  const { data: existingGifts } = await supabaseAdmin
    .from('user_gifts')
    .select('gift_threshold_id')
    .eq('user_id', userId)

  const existingThresholdIds = existingGifts?.map(g => g.gift_threshold_id) || []

  // Filter out already assigned gifts
  const giftsToAssign = newlyReachedThresholds.filter(threshold =>
    !existingThresholdIds.includes(threshold.id)
  )

  if (giftsToAssign.length === 0) {
    console.log('🎁 Gift System: All reached thresholds already assigned')
    return
  }

  // Assign new gifts
  for (const threshold of giftsToAssign) {
    try {
      const { error } = await supabaseAdmin
        .from('user_gifts')
        .insert({
          user_id: userId,
          gift_threshold_id: threshold.id,
          products_received: threshold.gift_product_ids || [],
          points_used: threshold.threshold_points,
          order_id: orderId,
          notes: `Gift automatically assigned for reaching ${threshold.threshold_points} points`
        })

      if (error) {
        console.error(`🎁 Gift System: Error assigning gift for threshold ${threshold.id}:`, error)
      } else {
        console.log(`🎁 Gift System: Successfully assigned gift for ${threshold.threshold_points} points to user ${userId}`)
      }
    } catch (error) {
      console.error(`🎁 Gift System: Exception assigning gift for threshold ${threshold.id}:`, error)
    }
  }
}

/**
 * Calculate discount for user level (now league-based)
 */
export function calculateLevelDiscount(amount: number, level: UserLevel | null): number {
  if (!level || !level.discount_percentage) return 0
  return amount * (level.discount_percentage / 100)
}

/**
 * Calculate league-based discount for user
 */
export async function calculateLeagueDiscount(amount: number, userLevel: number): Promise<number> {
  const league = calculateLeague(userLevel)
  const discountPercentage = await getLeagueDiscount(league)
  return amount * (discountPercentage / 100)
}

/**
 * Get user's discount percentage (league-based)
 */
export async function getUserDiscountPercentage(userId: string): Promise<number> {
  const supabase = await createClient()

  const { data: user } = await supabase
    .from('users')
    .select('total_points, current_level')
    .eq('id', userId)
    .single()

  if (!user) return 0

  const level = user.current_level || 1
  const league = calculateLeague(level)
  return await getLeagueDiscount(league)
}

/**
 * Initialize default user levels if none exist
 */
export async function initializeDefaultLevels(): Promise<void> {
  const supabase = await createClient()
  
  // Check if levels already exist
  const { count } = await supabase
    .from('user_levels')
    .select('*', { count: 'exact', head: true })

  if (count && count > 0) return

  // Create default levels
  const defaultLevels = [
    {
      level: 1,
      name: 'Bronze',
      minimum_points: 0,
      discount_percentage: 0,
      points_multiplier: 1.0
    },
    {
      level: 2,
      name: 'Silver',
      minimum_points: 200,
      discount_percentage: 5,
      points_multiplier: 1.2
    },
    {
      level: 3,
      name: 'Gold',
      minimum_points: 500,
      discount_percentage: 10,
      points_multiplier: 1.5
    },
    {
      level: 4,
      name: 'Platinum',
      minimum_points: 1000,
      discount_percentage: 15,
      points_multiplier: 2.0
    }
  ]

  await supabase
    .from('user_levels')
    .insert(defaultLevels)
}

/**
 * Initialize default gift thresholds if none exist
 */
export async function initializeDefaultGiftThresholds(): Promise<void> {
  const supabase = await createClient()
  
  // Check if thresholds already exist
  const { count } = await supabase
    .from('gift_thresholds')
    .select('*', { count: 'exact', head: true })

  if (count && count > 0) return

  // Get some product IDs for gifts (you'll need to adjust these)
  const { data: products } = await supabase
    .from('products')
    .select('id')
    .eq('category', 'accessories')
    .limit(3)

  if (!products || products.length === 0) return

  // Create default gift thresholds (based on points)
  const defaultThresholds = [
    {
      threshold_points: 75,
      gift_product_ids: [products[0]?.id].filter(Boolean),
      is_active: true
    },
    {
      threshold_points: 150,
      gift_product_ids: products.slice(0, 2).map(p => p.id),
      is_active: true
    },
    {
      threshold_points: 250,
      gift_product_ids: products.map(p => p.id),
      is_active: true
    }
  ]

  await supabase
    .from('gift_thresholds')
    .insert(defaultThresholds)
}
