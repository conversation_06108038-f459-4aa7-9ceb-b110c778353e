'use client'

import { useEffect, useState, useCallback, useMemo } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useTranslations, useLocale } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Trophy, Gift, Star, Plus, Crown } from 'lucide-react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { AdminBackButton } from '@/components/admin/admin-back-button'

import { GiftThresholdActions } from '@/components/admin/gift-threshold-actions'
import { useToast } from '@/hooks/use-toast'
import LeagueManagement from '@/components/admin/league-management'

export default function AdminGamificationPage() {
  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [saving, setSaving] = useState(false)
  const [pointsPerChf, setPointsPerChf] = useState<number>(1.0)
  const [userLevels, setUserLevels] = useState<Array<{
    id: string;
    level: number;
    name: string;
    minimum_points: number;
    discount_percentage: number;
    points_multiplier: number;
  }>>([])
  const [giftThresholds, setGiftThresholds] = useState<Array<{
    id: string;
    threshold_points: number;
    gift_product_ids: string[];
    is_active: boolean;
  }>>([])
  const { toast } = useToast()

  const loadGamificationData = useCallback(async () => {
    try {
      console.log('Loading gamification data...')

      // Get user levels
      const { data: userLevelsData, error: levelsError } = await supabase
        .from('user_levels')
        .select('*')
        .order('level', { ascending: true })

      if (levelsError) {
        console.error('Error fetching user levels:', levelsError)
      }

      // Get gift thresholds
      const { data: giftThresholdsData, error: giftsError } = await supabase
        .from('gift_thresholds')
        .select('*')
        .order('threshold_points', { ascending: true })

      if (giftsError) {
        console.error('Error fetching gift thresholds:', giftsError)
      }

      // Get points per CHF setting
      const { data: settingsData, error: settingsError } = await supabase
        .from('site_settings')
        .select('points_per_chf')
        .maybeSingle()

      if (settingsError) {
        console.error('Error fetching settings:', settingsError)
      }

      setUserLevels(userLevelsData || [])
      setGiftThresholds(giftThresholdsData || [])
      setPointsPerChf(settingsData?.points_per_chf || 1.0)
      setLoading(false)
      console.log('Gamification data loaded successfully')
    } catch (error) {
      console.error('Error loading gamification data:', error)
      setLoading(false)
    }
  }, [supabase])

  const handlePointsPerChfSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    try {
      const formData = new FormData(e.target as HTMLFormElement)
      const newPointsPerChf = parseFloat(formData.get('points-per-chf') as string) || 1.0

      const { error } = await supabase
        .from('site_settings')
        .upsert({
          id: 1, // Assuming single settings record
          points_per_chf: newPointsPerChf
        })

      if (error) {
        throw error
      }

      setPointsPerChf(newPointsPerChf)
      toast({
        title: t('saveSuccess'),
        description: t('saveSuccessMessage'),
      })
    } catch (error) {
      console.error('Error saving points per CHF:', error)
      toast({
        title: t('saveError'),
        description: t('saveErrorMessage'),
        variant: 'destructive',
      })
    } finally {
      setSaving(false)
    }
  }

  useEffect(() => {
    async function checkAuthAndLoadData() {
      try {
        if (authChecked) return

        console.log('Checking authentication...')
        const { data: { user }, error: authError } = await supabase.auth.getUser()

        if (authError) {
          console.error('Auth error:', authError)
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        if (!user) {
          console.log('No user found, redirecting to login')
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        console.log('User found:', user.email)

        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()

        if (profileError) {
          console.error('Profile error:', profileError)
          setAuthChecked(true)
          setLoading(false)
          return
        }

        console.log('Profile loaded:', profile)

        if (!profile?.is_admin) {
          console.log('User is not admin, redirecting')
          setAuthChecked(true)
          router.push(`/${locale}`)
          return
        }

        console.log('User is admin, loading gamification data')
        setAuthChecked(true)
        await loadGamificationData()
      } catch (error) {
        console.error('Error in checkAuthAndLoadData:', error)
        setAuthChecked(true)
        setLoading(false)
      }
    }

    if (!authChecked) {
      checkAuthAndLoadData()
    }
  }, [authChecked, locale, router, supabase, loadGamificationData])

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">{t('gamificationPage.loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      {/* Back to Dashboard Button */}
      <div className="mb-6">
        <AdminBackButton />
      </div>

      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">{t('gamificationPage.title')}</h1>
          <p className="text-muted-foreground">
            {t('gamificationPage.subtitle')}
          </p>
        </div>
      </div>

      {/* Points Configuration */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            {t('gamificationSettings.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handlePointsPerChfSubmit} className="space-y-4">
            <div>
              <Label htmlFor="points-per-chf">{t('gamificationSettings.pointsPerCHF')}</Label>
              <Input
                id="points-per-chf"
                name="points-per-chf"
                type="number"
                step="0.1"
                min="0"
                defaultValue={pointsPerChf}
                placeholder="1.0"
                className="max-w-xs"
              />
              <p className="text-sm text-muted-foreground mt-1">
                {t('gamificationSettings.pointsPerCHFDesc')}
              </p>
            </div>
            <Button type="submit" disabled={saving}>
              {saving ? t('saving') : t('save')}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* League Management */}
      <LeagueManagement onConfigUpdated={loadGamificationData} />

      {/* League System Info */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">League-Based Gamification System</h3>
            <p className="text-blue-800 text-sm mb-2">
              The new simplified system organizes customers into leagues based on their level:
            </p>
            <ul className="text-blue-800 text-sm space-y-1">
              <li>• <strong>Bronze League:</strong> Levels 1-10 (0% discount)</li>
              <li>• <strong>Silver League:</strong> Levels 11-20 (5% discount)</li>
              <li>• <strong>Gold League:</strong> Levels 21-30 (10% discount)</li>
              <li>• <strong>Platinum League:</strong> Levels 31-40 (15% discount)</li>
              <li>• <strong>Diamond League:</strong> Levels 41-50+ (20% discount)</li>
            </ul>
            <p className="text-blue-800 text-sm mt-2">
              Discounts are applied automatically at checkout based on the customer&apos;s current league.
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Current Levels Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5" />
              Current League Levels
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Progressive levels with league-based discounts (read-only)
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {userLevels?.slice(0, 20).map((level) => {
                const getLeagueInfo = (levelNum: number) => {
                  if (levelNum <= 10) return { name: 'Bronze', color: 'text-amber-600', icon: '🥉' }
                  if (levelNum <= 20) return { name: 'Silver', color: 'text-gray-500', icon: '🥈' }
                  if (levelNum <= 30) return { name: 'Gold', color: 'text-yellow-500', icon: '🥇' }
                  if (levelNum <= 40) return { name: 'Platinum', color: 'text-purple-500', icon: '💎' }
                  return { name: 'Diamond', color: 'text-blue-500', icon: '💠' }
                }

                const league = getLeagueInfo(level.level)

                return (
                  <div key={level.id} className="flex items-center justify-between p-3 border rounded-lg bg-gray-50">
                    <div className="flex items-center gap-3">
                      <span className="text-lg">{league.icon}</span>
                      <div>
                        <h4 className="font-medium">{level.name}</h4>
                        <p className="text-xs text-muted-foreground">
                          {level.minimum_points} points • {league.name} League
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{level.discount_percentage}% discount</p>
                    </div>
                  </div>
                )
              })}
              {userLevels && userLevels.length > 20 && (
                <p className="text-center text-sm text-muted-foreground py-2">
                  ... and {userLevels.length - 20} more levels
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Gift Thresholds */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Gift className="h-5 w-5" />
              {t('gamificationPage.giftThresholds')}
            </CardTitle>
            <Button size="sm" asChild>
              <Link href={`/${locale}/admin/gamification/gifts/create`}>
                <Plus className="mr-2 h-4 w-4" />
                {t('gamificationPage.newGift')}
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {giftThresholds?.map((gift) => (
                <div key={gift.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <div className="font-semibold">
                      {gift.gift_product_ids.length > 0
                        ? `${gift.gift_product_ids.length} ${gift.gift_product_ids.length === 1 ? 'Produkt' : 'Produkte'}`
                        : 'Kein Produkt'
                      }
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {t('gamificationPage.fromPoints', { points: gift.threshold_points })}
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <Badge variant={gift.is_active ? "default" : "secondary"}>
                      {gift.is_active ? t('gamificationPage.active') : t('gamificationPage.inactive')}
                    </Badge>
                    <GiftThresholdActions gift={gift} />
                  </div>
                </div>
              ))}
              {(!giftThresholds || giftThresholds.length === 0) && (
                <p className="text-muted-foreground text-center py-4">
                  {t('gamificationPage.noGiftsConfigured')}
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Gamification Statistics */}
      <div className="grid md:grid-cols-4 gap-4 mt-8">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('gamificationPage.activeLevels')}</p>
                <p className="text-2xl font-bold">{userLevels?.length || 0}</p>
              </div>
              <Trophy className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('gamificationPage.giftActions')}</p>
                <p className="text-2xl font-bold">{giftThresholds?.length || 0}</p>
              </div>
              <Gift className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('gamificationPage.maxDiscount')}</p>
                <p className="text-2xl font-bold">
                  {userLevels && userLevels.length > 0
                    ? `${Math.max(...userLevels.map(l => l.discount_percentage))}%`
                    : '0%'
                  }
                </p>
              </div>
              <Star className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('gamificationPage.maxPointsMultiplier')}</p>
                <p className="text-2xl font-bold">
                  {userLevels && userLevels.length > 0
                    ? `${Math.max(...userLevels.map(l => l.points_multiplier))}x`
                    : '1x'
                  }
                </p>
              </div>
              <Crown className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>


    </div>
  )
}
